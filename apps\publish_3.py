import logging
import e3series
import customtkinter as ctk
from tkinter import filedialog
import subprocess
import datetime
import json
import sys
import traceback
import os
import appdirs

# Check if running as a frozen executable
is_frozen = getattr(sys, 'frozen', False)

# Define utility functions directly in the script to avoid import issues
def get_app_dir():
    """Get the application directory, works for both development and PyInstaller"""
    try:
        # <PERSON>yInstaller creates a temp folder and stores path in _MEIPASS
        if is_frozen:
            base_path = sys._MEIPASS
        else:
            # We're running in a normal Python environment
            base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    except Exception:
        # Fallback to current directory
        base_path = os.path.abspath(os.path.dirname(sys.executable) if is_frozen else '.')
    return base_path

def get_user_data_dir():
    """Get the user data directory for storing configuration files"""
    return appdirs.user_data_dir("EngineeringTools", "Phoenix")

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    app_dir = get_app_dir()
    return os.path.join(app_dir, relative_path)

def get_config_path(filename):
    """Get path to a configuration file, checking user directory first, then app directory"""
    # First check in user data directory
    user_config = os.path.join(get_user_data_dir(), filename)
    if os.path.exists(user_config):
        return user_config

    # Then check in app resources/config directory
    app_config = os.path.join(get_app_dir(), 'resources', 'config', filename)
    if os.path.exists(app_config):
        return app_config

    # Finally check in app directory (for backward compatibility)
    return os.path.join(get_app_dir(), filename)

# Define theme utility function
def apply_theme(theme_name="red", appearance_mode="dark"):
    """Apply a theme to the application"""
    ctk.set_appearance_mode(appearance_mode)

    # Try to load theme from file if not frozen
    if not is_frozen:
        try:
            # Add parent directory to path to allow importing from lib and utils
            sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from lib.theme_utils import apply_theme as lib_apply_theme
            return lib_apply_theme(theme_name, appearance_mode)
        except ImportError as e:
            logging.warning(f"Could not import theme_utils: {e}")

    # Check if theme file exists directly
    try:
        # Get the application directory
        app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        # Construct the path to the theme file
        theme_path = os.path.join(app_dir, "resources", "themes", f"{theme_name}.json")

        # Check if the theme file exists
        if os.path.exists(theme_path):
            logging.info(f"Using theme file: {theme_path}")
            ctk.set_default_color_theme(theme_path)
            return
        else:
            logging.warning(f"Theme file not found: {theme_path}")
    except Exception as e:
        logging.error(f"Error loading theme file: {e}")

    # Fallback to built-in theme
    ctk.set_default_color_theme("red")
    logging.info(f"Using fallback built-in theme: {appearance_mode} mode with red color theme")

# Import ManualCreator and related utilities
try:
    # Add parent directory to path to allow importing from lib
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

    # Import the ManualCreator class
    from lib.manual_creator import ManualCreator
    logging.info("Successfully imported ManualCreator from lib.manual_creator")
except ImportError as e:
    logging.error(f"Error importing ManualCreator from lib: {e}")
    logging.error("Manual creation functionality will not be available")
    # Define a placeholder class that will raise an error if used
    class ManualCreator:
        def __init__(self, *args, **kwargs):
            raise ImportError("ManualCreator module could not be imported. Please ensure lib/manual_creator.py is properly installed.")

# Configure logging to both file and console
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("publish_debug.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

try:
    app = e3series.Application()
    logging.info("Starting application...")
    print("Starting application...")
    app.PutInfo(0, "Starting application...")

    # Load the MODELS dictionary from the JSON file using get_config_path
    json_file_path = get_config_path("models.json")
    logging.info(f"Attempting to load models from: {json_file_path}")
    print(f"Attempting to load models from: {json_file_path}")
    app.PutInfo(0, f"Attempting to load models from: {json_file_path}")

    try:
        with open(json_file_path, 'r') as json_file:
            MODELS = json.load(json_file)
        logging.info(f"Successfully loaded models. Categories: {list(MODELS.keys())}")
        print(f"Successfully loaded models. Categories: {list(MODELS.keys())}")
        app.PutInfo(0, f"Successfully loaded models. Categories: {list(MODELS.keys())}")
    except Exception as e:
        logging.error(f"Failed to load models.json: {str(e)}")
        print(f"ERROR: Failed to load models.json: {str(e)}")
        app.PutInfo(0, f"Failed to load models.json: {str(e)}")
        MODELS = {}

    # Apply the red theme
    apply_theme("red", "dark")

    class JobFolderApp:
        """
        An application to publish a project by reading title block data, updating attributes, and exporting to PDF.
        """
        def __init__(self, root: ctk.CTk) -> None:
            logging.info("Initializing JobFolderApp")
            print("Initializing JobFolderApp")
            app.PutInfo(0, "Initializing JobFolderApp")
            self.root = root
            self.root.title("Publish Project")
            self.root.geometry("600x500")  # Increased height to accommodate new checkboxes and entry

            self.fields = [
                "GSS Parent #",
                "Serial number",
                "Customer",
                "Location",
                "Title",
                "Sales order #",
            ]  # Removed "Model" from fields
            self.entries = {}

            # Add model variable and list
            self.model_var = ctk.StringVar()
            self.available_models = []

            # Title block attributes
            self.sheet_document_number = ""
            self.sheet_model = ""
            self.sheet_description = ""
            self.sheet_customer = ""
            self.sheet_location = ""
            self.sheet_sales_order = ""
            self.sheet_serial_number = ""
            self.folder_selected = ""
            self.serial_folder_path = ""
            self.parent_number = ""

            # Create checkbox variables
            self.create_manual_var = ctk.BooleanVar(value=False)
            self.fill_series_var = ctk.BooleanVar(value=False)

            try:
                self.create_form()
                self.read_titleblock_data()
                self.populate_fields()
                logging.info("GUI initialization complete")
                print("GUI initialization complete")
                app.PutInfo(0, "GUI initialization complete")
            except Exception as e:
                logging.error(f"Error during GUI initialization: {str(e)}")
                print(f"ERROR: Error during GUI initialization: {str(e)}")
                app.PutInfo(0, f"Error during GUI initialization: {str(e)}")
                logging.error(traceback.format_exc())
                print(f"ERROR: {traceback.format_exc()}")
                app.PutInfo(0, traceback.format_exc())

        def get_models_for_gss(self, gss_number: str) -> list:
            """Get all models that have the specified GSS Parent number."""
            matching_models = []
            for category in MODELS:
                for model, details in MODELS[category].items():
                    # Check if details is a list/tuple (old format) or dict (new format)
                    controls_parent = None
                    if isinstance(details, (list, tuple)) and len(details) > 3:
                        controls_parent = details[3]  # Assuming index 3 is controls_parent
                    elif isinstance(details, dict):
                        controls_parent = details.get('controls_parent')

                    if controls_parent == gss_number:
                        matching_models.append(model)
                        logging.debug(f"Found matching model {model} for GSS parent {gss_number}")
            return sorted(matching_models)

        def update_model_dropdown(self, *_):
            """Update the model dropdown based on the GSS Parent number."""
            gss_number = self.entries["GSS Parent #"].get()
            self.available_models = self.get_models_for_gss(gss_number)

            if self.available_models:
                self.model_dropdown.configure(values=self.available_models)
                self.model_var.set(self.available_models[0])
            else:
                self.model_dropdown.configure(values=["No matching models"])
                self.model_var.set("No matching models")

        def create_form(self) -> None:
            """
            Create the input form with fields and buttons.
            """
            frame = ctk.CTkFrame(self.root)
            frame.pack(fill="both", expand=True, padx=10, pady=10)

            # Create form fields
            for field in self.fields:
                row_frame = ctk.CTkFrame(frame)
                row_frame.pack(fill="x", padx=5, pady=5)

                label = ctk.CTkLabel(row_frame, text=field + ":", width=25)
                label.pack(side="left", padx=5)

                entry = ctk.CTkEntry(row_frame, width=200)
                entry.pack(side="right", fill="x", expand=True, padx=5)
                self.entries[field] = entry

                # Add trace to GSS Parent # field
                if field == "GSS Parent #":
                    entry.bind('<KeyRelease>', self.update_model_dropdown)

            # Create Model dropdown
            model_frame = ctk.CTkFrame(frame)
            model_frame.pack(fill="x", padx=5, pady=5)

            model_label = ctk.CTkLabel(model_frame, text="Model:", width=25)
            model_label.pack(side="left", padx=5)

            self.model_dropdown = ctk.CTkOptionMenu(
                model_frame,
                variable=self.model_var,
                values=["Select GSS Parent #"],
                width=200
            )
            self.model_dropdown.pack(side="right", fill="x", expand=True, padx=5)

            # Create checkbox for manual creation
            checkbox_frame = ctk.CTkFrame(frame)
            checkbox_frame.pack(fill="x", padx=5, pady=5)

            self.manual_checkbox = ctk.CTkCheckBox(
                checkbox_frame,
                text="Create Manual",
                variable=self.create_manual_var
            )
            self.manual_checkbox.pack(side="left", padx=5)

            # Create checkbox for fill series
            series_frame = ctk.CTkFrame(frame)
            series_frame.pack(fill="x", padx=5, pady=5)

            self.fill_series_checkbox = ctk.CTkCheckBox(
                series_frame,
                text="Fill Series",
                variable=self.fill_series_var
            )
            self.fill_series_checkbox.pack(side="left", padx=5)

            # Create entry for series count
            series_count_label = ctk.CTkLabel(series_frame, text="Count:", width=50)
            series_count_label.pack(side="left", padx=(20, 5))

            self.series_count_entry = ctk.CTkEntry(series_frame, width=80, placeholder_text="1")
            self.series_count_entry.pack(side="left", padx=5)
            self.series_count_entry.insert(0, "1")  # Default value

            # Create a frame for buttons
            button_frame = ctk.CTkFrame(frame)
            button_frame.pack(fill="x", padx=5, pady=5)

            browse_button = ctk.CTkButton(button_frame, text="Browse", command=self.browse_folder)
            browse_button.pack(side="left", padx=5)

            # Create a separate frame for folder label
            folder_frame = ctk.CTkFrame(frame)
            folder_frame.pack(fill="both", expand=True, padx=5, pady=5)

            self.folder_label = ctk.CTkLabel(folder_frame, text="No folder selected", width=200, wraplength=550, justify="left")
            self.folder_label.pack(fill="both", expand=True, padx=10, pady=5)

            publish_button = ctk.CTkButton(frame, text="Publish", command=self.publish)
            publish_button.pack(side="bottom", padx=5)

        def browse_folder(self) -> None:
            """
            Open a folder selection dialog and update the UI with the chosen folder.
            """
            folder = filedialog.askdirectory()
            if folder:
                self.folder_selected = folder
                self.folder_label.configure(text=f"Selected folder: {folder}", wraplength=550)
                logging.info(f"Selected folder: {folder}")
                print(f"Selected folder: {folder}")
                #app.PutInfo(0, f"Selected folder: {folder}")

        def read_titleblock_data(self) -> None:
            """
            Read title block data from the project via COM and store the values.
            """
            try:
                app = e3series.Application()
                job = app.CreateJobObject()
                dtm_sheet = job.CreateSheetObject()
                sheet_ids = job.GetAllSheetIds()

                for sheet in sheet_ids:
                    if isinstance(sheet, tuple):
                        for sheet_id in sheet:
                            if sheet_id is None:
                                continue
                            try:
                                dtm_sheet.SetId(sheet_id)
                                document_number = dtm_sheet.GetAttributeValue("DOCUMENT_NUMBER")
                                if document_number == "":
                                    continue
                                self.sheet_document_number = document_number
                                self.sheet_model = dtm_sheet.GetAttributeValue("Title1")
                                self.sheet_description = dtm_sheet.GetAttributeValue("Title2")
                                self.sheet_customer = dtm_sheet.GetAttributeValue("CUSTOMER")
                                self.sheet_location = dtm_sheet.GetAttributeValue("ORDER")
                                self.sheet_sales_order = dtm_sheet.GetAttributeValue("ORDER_NUMBER")
                                self.sheet_serial_number = dtm_sheet.GetAttributeValue("DRAWINGNUMBER")
                                break
                            except Exception as exc:
                                logging.error(f"Error processing sheet ID {sheet_id}: {exc}")
                    else:
                        logging.info(f"Sheet info: {sheet}")
            except Exception as exc:
                logging.error(f"An error occurred reading title block data: {exc}")
            finally:
                app = None
                job = None
                dtm_sheet = None
                sheet_ids = None

        def populate_fields(self) -> None:
            """
            Populate the form fields with the retrieved title block data.
            """
            self.entries["GSS Parent #"].insert(0, self.sheet_document_number)
            self.entries["Serial number"].insert(0, self.sheet_serial_number)
            self.entries["Customer"].insert(0, self.sheet_customer)
            self.entries["Location"].insert(0, self.sheet_location)
            self.entries["Title"].insert(0, self.sheet_description)

            # Update model dropdown after populating GSS Parent #
            self.update_model_dropdown()

            # If sheet_model matches one of the available models, select it
            if self.sheet_model in self.available_models:
                self.model_var.set(self.sheet_model)

        def write_gss_parent(self, parent_number: str, serial: str, app) -> None:
            """
            Write the GSS Parent number attribute to all devices in the project.
            """
            try:
                job = app.CreateJobObject()
                device = job.CreateDeviceObject()
                device_ids = job.GetAllDeviceIds()

                job.SetAttributeValue("GSS_PARENT", f"{parent_number} {serial}")

                for dev in device_ids:
                    if isinstance(dev, tuple):
                        for dev_id in dev:
                            if dev_id is None:
                                continue
                            try:
                                device.SetId(dev_id)
                                device.SetAttributeValue("GSS_PARENT", f"{parent_number} {serial}")
                                logging.info(f"Updated Device ID: {dev_id}")
                            except Exception as exc:
                                logging.error(f"Error updating device ID {dev_id}: {exc}")
                    else:
                        logging.info(f"Device info: {dev}")
            except Exception as exc:
                logging.error(f"An error occurred in write_gss_parent: {exc}")
            finally:
                app = None
                job = None
                device = None
                device_ids = None

        def write_sheet_title_block_data(self, app) -> None:
            """
            Write the title block data attributes to the sheets in the project.
            """
            try:
                job = app.CreateJobObject()
                dtm_sheet = job.CreateSheetObject()
                sheet_ids = job.GetAllSheetIds()

                for sheet in sheet_ids:
                    if isinstance(sheet, tuple):
                        for sheet_id in sheet:
                            if sheet_id is None:
                                continue
                            try:
                                dtm_sheet.SetId(sheet_id)
                                dtm_sheet.SetAttributeValue("DOCUMENT_NUMBER", self.sheet_document_number)
                                dtm_sheet.SetAttributeValue("Title1", self.sheet_model)
                                dtm_sheet.SetAttributeValue("Title2", self.sheet_description)
                                dtm_sheet.SetAttributeValue("CUSTOMER", self.sheet_customer)
                                dtm_sheet.SetAttributeValue("ORDER", self.sheet_location)
                                dtm_sheet.SetAttributeValue("ORDER_NUMBER", self.sheet_sales_order)
                                dtm_sheet.SetAttributeValue("DRAWINGNUMBER", self.sheet_serial_number)
                                logging.info(f"Updated Sheet ID: {sheet_id}")
                            except Exception as exc:
                                logging.error(f"Error updating sheet ID {sheet_id}: {exc}")
                    else:
                        logging.info(f"Sheet info: {sheet}")
            except Exception as exc:
                logging.error(f"An error occurred in write_sheet_title_block_data: {exc}")
            finally:
                app = None
                job = None
                dtm_sheet = None
                sheet_ids = None

        def run_reportgenerator(self, app) -> None:
            """
            Run the ReportGenerator Creator to export a GSS BOM to excel.
            """
            try:
                message = "Starting the ReportGenerator - Creator"
                app.PutInfo(0, message)
                print(message)

                pid = app.GetProcessProperty("ProcessID")
                report_name = "GSS BOM"
                export_format = "ExcelExport"
                now = datetime.datetime.now()
                export_path = f"T:\\ENG\\Common\\GSS ELEC CAD BOMS\\{self.sheet_document_number} {self.sheet_serial_number} {now:%Y-%m-%d %H.%M}.xlsx"

                message = f'PID: {pid}, Report name: {report_name}, Export format: {export_format} Export path: {export_path}'
                logging.info(message)

                command = [
                    r"C:\Program Files\Zuken\E3.ReportGenerator\creatorbatch.exe",
                    "-c", r"C:\ProgramData\Zuken\E3.ReportGenerator\Configuration\E3.ReportGenerator.ini",
                    "--reportname", report_name,
                    "--exportformat", export_format,
                    "--e3pid", str(pid),
                    "--exportpath", export_path,
                ]

                result = subprocess.run(command, capture_output=True, text=True)
                logging.info(f"Command output: {result.stdout}")
                if result.stderr:
                    logging.error(f"Command errors: {result.stderr}")

            except Exception as e:
                logging.error(f"An error occurred: {e}")
            finally:
                pass

        def update_BOMs(self, app) -> None:
            """
            Run the ReportGenerator Creator to update the BOMs in the project.
            Also updates the drawing characteristic of all sheets to "Panel Configuration".
            """
            try:
                job = app.CreateJobObject()
                message = "Starting the ReportGenerator - Creator"
                app.PutInfo(0, message)
                print(message)


                # Now run the ReportGenerator Creator to update the BOMs
                pid = app.GetProcessProperty("ProcessID")
                report_names = ["PHOENIX BOM", "Simplified BOM"]
                export_format = "e3.sheet"
                for report in report_names:
                    command = [
                        r"C:\Program Files\Zuken\E3.ReportGenerator\creatorbatch.exe",
                        "-c", r"C:\ProgramData\Zuken\E3.ReportGenerator\Configuration\E3.ReportGenerator.ini",
                        "--reportname", report,
                        "--exportformat", export_format,
                        "--e3pid", str(pid),
                    ]
                    result = subprocess.run(command, capture_output=True, text=True)
                    logging.info(f"Command output: {result.stdout}")
                    if result.stderr:
                        logging.error(f"Command errors: {result.stderr}")
                    app.PutInfo(0, f"Updated {report}")
                    print(f"Updated {report}")

            except Exception as e:
                logging.error(f"An error occurred: {e}")
            finally:
                pass
            # Next, update the drawing characteristic of all sheets to "Panel Configuration"
            try:
                dtm_sheet = job.CreateSheetObject()
                sheet_ids = job.GetAllSheetIds()

                message = "Updating drawing characteristic of all sheets to 'Panel Configuration'"
                app.PutInfo(0, message)
                print(message)
                logging.info(message)

                for sheet in sheet_ids:
                    if isinstance(sheet, tuple):
                        for sheet_id in sheet:
                            if sheet_id is None:
                                continue
                            try:
                                dtm_sheet.SetId(sheet_id)
                                if dtm_sheet.GetAttributeValue("Document Type") == "BOM":
                                    dtm_sheet.SetAttributeValue("Characteristic", "Panel Configuration")
                                    logging.info(f"Updated Drawing Characteristic for Sheet ID: {sheet_id}")
                            except Exception as exc:
                                logging.error(f"Error updating Drawing Characteristic for sheet ID {sheet_id}: {exc}")
                    else:
                        logging.info(f"Sheet info: {sheet}")

                app.PutInfo(0, "Drawing characteristic update completed")
                print("Drawing characteristic update completed")
            except Exception as exc:
                logging.error(f"An error occurred updating drawing characteristics: {exc}")



        def export_to_pdf(self, job) -> None:
            """
            Export the project to a PDF file in the serial number folder.
            """
            try:
                # Create the PDF filename with document number and serial number in the serial number folder
                pdf_file = os.path.join(self.serial_folder_path, f"{self.sheet_document_number} {self.sheet_serial_number}.pdf")
                sheet_ids = job.GetAllSheetIds()
                options = 0x4800C  # Acrobat Version 12

                # Export all sheets to PDF
                result = job.ExportPDF(pdf_file, sheet_ids[1], options, "")
                if result[0] == 1:
                    logging.info(f"PDF successfully exported to {pdf_file}")
                else:
                    logging.error(f"Failed to export PDF. Error code: {result}")
            except Exception as exc:
                logging.error(f"An error occurred during PDF export: {exc}")

        def export_to_dxf(self, app, job) -> None:
            # New method to export any sheet with "Document Type" = "101 - Dataplates" to DXF
            try:
                dtm_sheet = job.CreateSheetObject()
                sheet_ids = job.GetAllSheetIds()
                # Options for DXF export if needed in the future

                # Create dataplates directory as a subfolder of the selected folder
                dataplates_dir = os.path.join(self.folder_selected, "DataPlates")
                if not os.path.exists(dataplates_dir):
                    os.makedirs(dataplates_dir)
                    logging.info(f"Created dataplates directory at {dataplates_dir}")

                for sheet in sheet_ids:
                    if isinstance(sheet, tuple):
                        for sheet_id in sheet:
                            if sheet_id is None:
                                continue
                            dtm_sheet.SetId(sheet_id)
                            doc_type = dtm_sheet.GetAttributeValue("Document Type")
                            if doc_type == "101 - Dataplates":
                                dxf_file = os.path.join(dataplates_dir, f"{self.sheet_document_number}_{self.sheet_serial_number}_{sheet_id}.dxf")
                                result = dtm_sheet.Export("DXF", 2018, dxf_file, "2")
                                if result == 0:
                                    logging.info(f"DXF successfully exported to {dxf_file}")
                                    app.PutInfo(0, f"DXF successfully exported to {dxf_file}")
                                else:
                                    logging.error(f"Failed to export DXF for sheet ID {sheet_id}. Error code: {result}")
                                    app.PutInfo(0, f"Failed to export DXF for sheet ID {sheet_id}. Error code: {result}")
            except Exception as exc:
                logging.error(f"An error occurred during DXF export: {exc}")

        def get_model(self) -> str:
            """Get the currently selected model."""
            return self.model_var.get()

        def save_job_data_to_json(self) -> None:
            """
            Save all job data to a JSON file in the serial number folder.
            """
            try:
                # Get the CT.Application instance
                ct_app = e3series.Application()

                # Create a dictionary with all form data
                job_data = {
                    "gss_parent": self.sheet_document_number,
                    "serial_number": self.sheet_serial_number,
                    "customer": self.sheet_customer,
                    "location": self.sheet_location,
                    "title": self.sheet_description,
                    "sales_order": self.sheet_sales_order,
                    "model": self.sheet_model,
                    "job_folder": self.serial_folder_path,
                    "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }

                # Create the JSON filename with document number and serial number in the serial number folder
                json_file = os.path.join(self.serial_folder_path, f"{self.sheet_document_number} {self.sheet_serial_number}.json")

                # Save the data to a JSON file
                with open(json_file, 'w') as f:
                    json.dump(job_data, f, indent=4)

                logging.info(f"Job data saved to {json_file}")
                print(f"Job data saved to {json_file}")
                ct_app.PutInfo(0, f"Job data saved to {json_file}")
                return True
            except Exception as e:
                logging.error(f"Failed to save job data to JSON: {str(e)}")
                print(f"ERROR: Failed to save job data to JSON: {str(e)}")
                try:
                    ct_app = e3series.Application()
                    ct_app.PutInfo(0, f"Failed to save job data to JSON: {str(e)}")
                except:
                    pass
                return False

        def publish(self) -> None:
            """
            Publish the project and optionally create the manual.
            If Fill Series is checked, publish multiple serial numbers in sequence.
            """
            if not self.folder_selected:
                logging.error("No folder selected for project export.")
                return

            # Check if Fill Series is enabled
            if self.fill_series_var.get():
                try:
                    series_count = int(self.series_count_entry.get())
                    if series_count <= 0:
                        logging.error("Series count must be a positive number.")
                        return
                except ValueError:
                    logging.error("Invalid series count. Please enter a valid number.")
                    return

                # Get the starting serial number
                try:
                    base_serial = self.entries["Serial number"].get()
                    # Try to extract numeric part from serial number
                    if base_serial.isdigit():
                        start_serial = int(base_serial)
                    else:
                        # Handle alphanumeric serial numbers - extract trailing digits
                        import re
                        match = re.search(r'(\d+)$', base_serial)
                        if match:
                            numeric_part = match.group(1)
                            prefix = base_serial[:-len(numeric_part)]
                            start_serial = int(numeric_part)
                        else:
                            logging.error("Cannot determine numeric part of serial number for series.")
                            return
                except Exception as e:
                    logging.error(f"Error parsing serial number: {e}")
                    return

                # Publish each serial number in the series
                for i in range(series_count):
                    current_serial = start_serial + i
                    if 'prefix' in locals():
                        current_serial_str = f"{prefix}{current_serial:0{len(numeric_part)}d}"
                    else:
                        current_serial_str = str(current_serial)

                    logging.info(f"Publishing serial number {i+1} of {series_count}: {current_serial_str}")

                    # Update the serial number in the entry field temporarily
                    original_serial = self.entries["Serial number"].get()
                    self.entries["Serial number"].delete(0, 'end')
                    self.entries["Serial number"].insert(0, current_serial_str)

                    # Publish this serial number
                    self.publish_single()

                    # Restore original serial for next iteration (will be incremented)
                    if i < series_count - 1:  # Don't restore on last iteration
                        self.entries["Serial number"].delete(0, 'end')
                        self.entries["Serial number"].insert(0, original_serial)

                logging.info(f"Series publishing completed. Published {series_count} serial numbers.")
                try:
                    app = e3series.Application()
                    app.PutInfo(0, f"SERIES PUBLISH COMPLETED - {series_count} units published")
                except:
                    pass
            else:
                # Single publish
                self.publish_single()

        def publish_single(self) -> None:
            """
            Publish a single project instance.
            """
            try:
                app = e3series.Application()
                # Update title block data from UI
                self.sheet_document_number = self.entries["GSS Parent #"].get()
                self.sheet_model = self.get_model()
                self.sheet_description = self.entries["Title"].get()
                self.sheet_customer = self.entries["Customer"].get()
                self.sheet_location = self.entries["Location"].get()
                self.sheet_sales_order = self.entries["Sales order #"].get()
                self.sheet_serial_number = self.entries["Serial number"].get()  # Make sure we get the serial number

                # Create serial number folder inside the selected folder
                self.serial_folder_path = os.path.join(self.folder_selected, self.sheet_serial_number)
                if not os.path.exists(self.serial_folder_path):
                    os.makedirs(self.serial_folder_path)
                    logging.info(f"Created serial number folder at {self.serial_folder_path}")

                # Save path with document number and serial number in the serial number folder
                save_path = os.path.join(self.serial_folder_path, f"{self.sheet_document_number} {self.sheet_serial_number}.e3s")

                # Write data to the project
                self.write_gss_parent(self.sheet_document_number, self.sheet_serial_number, app)
                self.write_sheet_title_block_data(app)
                self.run_reportgenerator(app)
                self.update_BOMs(app)

                # Save and export the project
                job = app.CreateJobObject()
                job.SaveAs(save_path)
                logging.info(f"Project saved to {save_path}")
                self.export_to_pdf(job)
                self.export_to_dxf(app, job)

                # Save job data to JSON file
                self.save_job_data_to_json()

                if self.create_manual_var.get():
                    model = self.sheet_model.strip()  # Remove any whitespace
                    category = self.determine_category(model)

                    if category:
                        logging.info(f"Creating manual for model {model} in category {category}")
                        try:
                            # Get the model data from the MODELS dictionary
                            model_data = MODELS[category][model]
                            logging.debug(f"Model data for {model}: {model_data}")

                            # Validate model data format
                            if isinstance(model_data, dict):
                                # New format: dictionary with keys
                                template_path = model_data.get('template_path')
                                drawings_path = model_data.get('drawings_path')
                                asme_flag = model_data.get('asme_flag', False)

                                # Convert to tuple format expected by ManualCreator
                                model_data_tuple = (template_path, drawings_path, asme_flag)
                                logging.debug(f"Converted model data to tuple: {model_data_tuple}")
                            elif isinstance(model_data, (list, tuple)) and len(model_data) >= 3:
                                # Old format: tuple/list with values
                                model_data_tuple = model_data
                            else:
                                raise ValueError(f"Invalid model data format for {model}")

                            # Create the manual using the ManualCreator class
                            manual_creator = ManualCreator(
                                job_folder=self.serial_folder_path,
                                category=category,
                                model=model,
                                company=self.sheet_customer,
                                location=self.sheet_location,
                                serial=self.sheet_serial_number,
                                model_data=model_data_tuple,
                                document_number=self.sheet_document_number
                            )

                            # Create the manual
                            result = manual_creator.create_manual()

                            if result:
                                logging.info(f"Manual successfully created for {model}")
                                app.PutInfo(0, f"Manual successfully created for {model}")
                            else:
                                logging.warning(f"Manual creation completed but may have issues for {model}")
                                app.PutInfo(0, f"Manual creation completed but may have issues for {model}")

                        except Exception as e:
                            logging.error(f"Failed to create manual: {str(e)}")
                            logging.exception("Detailed error information:")
                            app.PutInfo(0, f"Failed to create manual: {str(e)}")
                    else:
                        logging.error(f"Could not find category for model {model}. Available categories: {list(MODELS.keys())}")
                        logging.error(f"Model value: '{model}'")
                        app.PutInfo(0, f"Could not find category for model {model}. Available categories: {list(MODELS.keys())}")

            except Exception as exc:
                logging.error(f"An error occurred during publish: {exc}")
                logging.exception("Detailed error information:")
            finally:
                message = f"PUBLISH COMPLETED for {self.sheet_serial_number}"
                app.PutInfo(0, message)
                print(message)
                job = None
                app = None


        def determine_category(self, model: str) -> str:
            """
            Determine the category based on the model number.
            Returns the category name or None if not found.
            Works with both old and new model data formats.
            """
            logging.debug(f"Searching for category for model: {model}")
            for category, models in MODELS.items():
                if model in models:
                    logging.debug(f"Found category {category} for model {model}")
                    return category
            logging.debug(f"No category found for model {model}")
            return None

    if __name__ == "__main__":
        try:
            # Handle PyInstaller frozen environment
            if getattr(sys, 'frozen', False):
                # Running as compiled executable
                # Add the _MEIPASS directory to the PATH environment variable
                os.environ['PATH'] = sys._MEIPASS + os.pathsep + os.environ['PATH']

                # Change working directory to the executable directory
                # This ensures that relative paths work correctly
                os.chdir(os.path.dirname(sys.executable))

                # Set up logging to a file in the user's temp directory
                log_file = os.path.join(os.path.expanduser('~'), 'publish_3.log')
                logging.basicConfig(
                    level=logging.DEBUG,
                    format="%(asctime)s [%(levelname)s] %(message)s",
                    handlers=[
                        logging.FileHandler(log_file),
                        logging.StreamHandler(sys.stdout)
                    ]
                )
                logging.info(f"Running from frozen environment: {sys.executable}")
        except Exception as e:
            # If there's an error in the PyInstaller-specific code, log it but continue
            print(f"Error setting up frozen environment: {str(e)}")

        try:
            logging.info("Creating main window")
            root = ctk.CTk()
            app = JobFolderApp(root)
            logging.info("Starting main loop")
            root.mainloop()
        except Exception as e:
            logging.error(f"Fatal error in main loop: {str(e)}")
            logging.error(traceback.format_exc())
            sys.exit(1)

except Exception as e:
    logging.error(f"Fatal error during startup: {str(e)}")
    logging.error(traceback.format_exc())

